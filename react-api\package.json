{"name": "react-api", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "local": " npm run dev"}, "dependencies": {"@emotion/react": "^11.10.8", "@emotion/styled": "^11.10.8", "@mui/icons-material": "^5.11.16", "@mui/joy": "^5.0.0-alpha.77", "@mui/material": "^5.12.2", "@react-pdf/renderer": "^3.3.4", "axios": "^1.3.6", "crypto-js": "^4.2.0", "echarts-for-react": "^3.0.2", "env-cmd": "^10.1.0", "http-proxy-middleware": "^2.0.6", "lodash.debounce": "^4.0.8", "mui-datatables": "^4.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "react-to-print": "^3.1.1", "tailwindcss-animated": "^1.1.2", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.23", "tailwindcss": "^3.3.2", "vite": "^4.3.2"}}