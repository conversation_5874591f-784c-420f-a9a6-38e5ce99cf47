<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Events\EntryDetailDeleted;

use App\Listeners\SubtractInventory;
use App\Models\Entry;
use App\Models\EntryGeneral;
use App\Models\HierarchyEntity;
use App\Models\Inventory;
use App\Models\InventoryGeneral;
use App\Models\Module;
use App\Models\Organization;
use App\Models\Output;
use App\Models\OutputGeneral;
use App\Models\Product;
use App\Models\ProductRelation;
use App\Models\User;
use Hash;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Throwable;

use function Laravel\Prompts\search;

class DatabaseSeeder extends Seeder
{

    public function run(): void
    {

           // $this->changePasswordUser('25783190');

        $this->restartDatabase('11sept2025');

        // $this->checkInventoryStatus();

        // $this->checkInventory();

        // $this->resolveInventory();

        // $this->call([

        //     ModuleSeeder::class,
        //     UserModuleSeeder::class,
        //     HierarchyEntitySeeder::class,
        //     UserSeeder::class,
        //     CategorySeeder::class,
        //     TypePresentationSeeder::class,
        //     TypeAdministrationSeeder::class,
        //     MedicamentSeeder::class,
        //     ConditionSeeder::class,
        //     MunicipalitySeeder::class,
        //     ParishSeeder::class,
        //     OrganizationSeeder::class,
        //     TypeActivitySeeder::class,

        // ]);

    }

    private function resolveInventory(){

        $entries = Entry::select('id')->whereIn('product_id', [
            709,
            465,
            74,
            279,
            935,
            53
        ])->whereHas('entryGeneral',function ($query) {
                $query->where('status', 2);
        })->get()->pluck('id')->toArray();

        Inventory::whereIn('entry_id', $entries)->update([
            'stock' => 0,
            'entries' => 0,
            'outputs' => 0,
        ]);



        $inventories = Inventory::select('id','entry_id','entries','stock','outputs')
        ->where('entity_code','1')
        ->get();

        foreach ($inventories as $inventory) {
            $entryToValidate = Entry::where('id', $inventory->entry_id)->first();

            if(!isset($entryToValidate->id)){
                $output = Output::where('inventory_id',$inventory->id)->first();
                if(isset($output->id))
                    throw new Exception("joda hay una salida", 500);
                else
                    $inventory->delete();
            }
        }

        $inventoryGenerals = InventoryGeneral::where('entity_code','1')->get();

        foreach ($inventoryGenerals as $inventoryGeneral) {

            $inventories = Inventory::where('entity_code','1')
            ->where('product_id', $inventoryGeneral->product_id)
            ->get();

             $totalEntries = $inventories->sum('entries');
            $totalStock = $inventories->sum('stock');
            $totalOutputs = $inventories->sum('outputs');


            $inventoryGeneral->update([
                'entries' => $totalEntries,
                'stock' => $totalStock,
                'stock_good' => $totalStock,
                'outputs' => $totalOutputs,
            ]);


        }



    }


    private function checkInventory(){


        $entries = Entry::with('entryGeneral')
        ->select('id','quantity')
        ->where('product_id', 709 )
        ->where('entity_code', '1')
        ->whereHas('entryGeneral',function ($query) {
                $query->where('status','!=', 2);
            })
        ->get();

        $inventories = Inventory::select('entry_id','entries','stock','outputs')
        ->where('product_id', 709 )
        ->where('entity_code','1')
        ->get();

        $inventoryGeneral = InventoryGeneral::select('stock_expired','stock_per_expire','stock_bad','stock_good','stock','entries','outputs')
        ->where('product_id', 709)
        ->where('entity_code', '1')
        ->first();

        Log::info('Entradas: '. $entries);
        Log::info('Inventario detallado: '. $inventories);
        Log::info('Inventario General: '. $inventoryGeneral);



    }

    private function checkInventoryStatus()
    {
        $products = Product::select('id', 'search')->get();



        foreach ($products as $product ) {

            $totalQuantity = Entry::with('entryGeneral')->where('entity_code', '1')->where('product_id', $product->id)
            ->whereHas('entryGeneral',function ($query) {
                $query->where('status','!=', 2);
            })->sum('quantity');

            $inventory = InventoryGeneral::where('product_id', $product->id)
            ->where('entity_code', '1')->first();
            if(!isset($inventory->id))
                continue;
            if($inventory->entries != $totalQuantity)
                Log::info('Malo: '
            . $product->id . ' '
            . $product->search
            . ', Entradas totales: '
            . $totalQuantity
            .', en inventario: '
            . $inventory->entries);

        }
        // Log::info('------- --------------- ------------');
        // Log::info('------- --------------- ------------');
        // Log::info('------- SALIDAS ------------');
        // Log::info('------- --------------- ------------');
        // Log::info('------- --------------- ------------');

        // foreach ($products as $product ) {

        //     $totalQuantity = Output::with('outputGeneral')->where('product_id', $product->id)
        //     ->whereHas('outputGeneral',function ($query) {
        //         $query->where('status','!=', 2);
        //     })->sum('quantity');

        //     $inventory = InventoryGeneral::where('product_id', $product->id)->first();
        //     if(!isset($inventory->id))
        //         continue;
        //     if($inventory->outputs != $totalQuantity)
        //         Log::info('Malo: ' . $inventory->id . ' '
        //     . $product->search
        //     . ', Salidas totales: '
        //     . $totalQuantity
        //     .', en inventario: '
        //     . $inventory->entries);

        // }

        Log::info('------- --------------- ------------');
        Log::info('------- --------------- ------------');
        Log::info('------- Inventario ------------');
        Log::info('------- --------------- ------------');
        Log::info('------- --------------- ------------');

        foreach ($products as $product ) {

            $totalQuantity = Inventory::where('product_id', $product->id)
            ->where('entity_code','1')
            ->sum('stock');

            $inventory = InventoryGeneral::where('product_id', $product->id)
            ->where('entity_code','1')
            ->first();
            if(!isset($inventory->id))
                continue;
            if($inventory->stock != $totalQuantity)
                Log::info('Malo: '
            . $product->id . ' '
            . $product->search
            . ', Inventario detallado total: '
            . $totalQuantity
            .', en inventario: '
            . $inventory->stock);

        }

       $discrepancies =  InventoryGeneral::where('entity_code','1')->whereRaw('(entries - outputs) != stock')->count();

       Log::info('discrepancias: ' . $discrepancies);

    }

    private function restartDatabase($filename){

        ini_set('memory_limit', '500M');

           DB::transaction(function () use($filename) {
            try {


                DB::unprepared(file_get_contents(database_path('sql/deleteAllTables.sql')));
                DB::unprepared(file_get_contents(database_path('sql/'.$filename.'.sql')));



            } catch (\Exception $e) {
                // Lanzar la excepción para que se realice el rollback
                Log::info('UN ERROR EN EL REINICIO DE DB');
                Log::error($e->getMessage());
                throw $e;
            }
        });




    }



    }





