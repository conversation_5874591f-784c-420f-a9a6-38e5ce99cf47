.permisos {
    margin-top: 25px; 
    h3{
        text-align: center;
        font-weight: bold;
        position: relative;
        top: 10px;
    }
    .module {
        // border-bottom: 1px solid rgb(221, 221, 221);
        list-style-type: disc;
        position: relative;
        padding-inline: 10px;
        li {
            // margin-left: 10px;
            background-color: #069DBF;
            display: inline-block;
            box-shadow: 2px 3px 16px rgba(0, 0, 0, 0.377);
            // color: #A64684;
            padding-top: 5px;
            // padding-bottom: 0; 
            padding-inline: 6px;
            color: rgb(255, 255, 255);
            font-weight: bold;
            position: relative;
            top: 23px;
            border-radius: 2px;
        }   
    }
    .checkbox_container {
        padding-block: 10px;
        margin-left: 20px;
        margin-top: 5px;
        padding-left: 10px;
        padding-top: 34px;
        border-radius: 5px;
        color: rgb(0, 0, 0);
        background: #ececec;
        // margin-bottom: 10px;
    }
    label {
        input {
            margin-right: 3px !important;
            &:checked + span {
                color: #069DBF;
                font-weight: bold;
            }
        }
        
        span {
            margin-right: 30px;
    
        }
    }
    label:hover {
        cursor: pointer;
        color: #A64684 !important;
        // font-weight: bold; 

    }
    b {
        margin-bottom: 35px !important;   
    }
}
