import React, { forwardRef } from "react";
import ministerioDeSalud from "../assets/img/ministerioDeSalud.jpg";
import años200 from "../assets/img/años200.jpg";

const InventoryReport2025 = forwardRef((props, ref) => {
  return (
    <div ref={ref}>
      <div className="flex justify-between items-center print-flex">
          <img
            src={ministerioDeSalud}
            alt="ministerioDeSalud"
            className="w-72 mt-2 h-max print-img-large"
          />
          <img src={años200} alt="años200" className="w-32 h-auto print-img-small" />
      </div>
      <h1
        className="text-center text-xl font-bold mb-3 print-title"
        style={{ color: "#1F4E78" }}
      >
        INVENTARIO ALMACENES REGIONALES
      </h1>
      <div className="text-center print-info-container">
        <div className="flex border border-dark text-sm print-info-row">
          <div
            className="w-1/4 p-1 text-center print-info-cell print-info-header"
            style={{ backgroundColor: "#1F4E78", color: "white" }}
          >
            <p>ESTADO</p>
          </div>
          <div className="w-1/4 p-1 font-semibold print-info-cell print-info-bold">
            <p>FALCÓN</p>
          </div>
          <div
            className="w-1/4 p-1 text-center print-info-cell print-info-header"
            style={{ backgroundColor: "#1F4E78", color: "white" }}
          >
            <p>MUNICIPIO</p>
          </div>
          <div className="w-1/4 p-1 font-semibold print-info-cell print-info-bold">
            <p>MIRANDA</p>
          </div>
        </div>
        <div className="flex border border-dark text-sm border-t-0 print-info-row print-info-no-top">
          <div
            className="w-1/4 p-1 text-center print-info-cell print-info-header"
            style={{ backgroundColor: "#1F4E78", color: "white" }}
          >
            <p>PARROQUIA</p>
          </div>
          <div className="w-1/4 p-1 font-semibold print-info-cell print-info-bold">
            <p>SAN ANTONIO</p>
          </div>
          <div
            className="w-1/4 p-1 text-center print-info-cell print-info-header"
            style={{ backgroundColor: "#1F4E78", color: "white" }}
          >
            <p>DIRECCIÓN</p>
          </div>
          <div className="w-1/4 p-1 font-semibold print-info-cell print-info-bold">
            <p>AV. EL TENIS, CORO 4101, FALCÓN</p>
          </div>
        </div>
      </div>
      <table
        className="print-table border border-t-0 border-dark"
        aria-label="simple table"
      >
        <thead className="print-header border-b border-dark">
          <tr className="print-header">
            <th className="print-header">DESCRIPCIÓN DE PRODUCTO</th>
            <th className="print-header">TIPO DE INSUMO</th>
            <th className="print-header">CANTIDAD</th>
            <th className="print-header">LOTE</th>
            <th className="print-header">FECHA DE VENCIMIENTO</th>
          </tr>
        </thead>
        <tbody>
          {props?.products.map((product, rowIndex) => {
            return (
              <tr key={rowIndex}>
                <td className="px-2 ">
                  {product.product_name.split(" ").slice(1).join(" ")}
                </td>
                <td className="px-1 text-xs">{product.type}</td>
                <td className="px-1">{product.quantity}</td>
                <td className="px-1">{product.lote_number}</td>
                <td className="px-1">{product.expiration_date}</td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
});

InventoryReport2025.displayName = 'InventoryReport2025';

export default InventoryReport2025;
