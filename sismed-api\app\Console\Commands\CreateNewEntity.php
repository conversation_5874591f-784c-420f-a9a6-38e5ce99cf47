<?php

namespace App\Console\Commands;

use App\Models\Organization;
use App\Models\HierarchyEntity;
use Illuminate\Console\Command;

class CreateNewEntity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:entity {name : Nombre de la entidad} {code : Codigo de jerarquia de la entidad}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Crea una nueva entidad (Inventario)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->argument('name');
        $code = $this->argument('code');

        $find = HierarchyEntity::where('code',$code)->first();

        if(isset($find->id))
        {
            $this->error('El codigo de esta entidad ya existe');
            return 0;
        }

         HierarchyEntity::create(
            [
                'code' => $code,
                'name' => $name,
            ]

        );

        Organization::create([

            'name' => $name,
            'code' => $code,
            'authority_fullname' => 'Sin asignar',
            'authority_ci' => '0000000',
            'municipality_id' => null,
            'parish_id' => null,
            'search' => $name . ' '. $code
        ]);

        $this->info('Entidad creada exitosamente');
    }
}
