<?php

namespace App\Listeners;

use App\Models\Product;
use App\Models\Inventory;
use App\Models\InventoryGeneral;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class DeleteInventoryFromEntry
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        $oldEntry = $event->oldEntry;

        $product = Product::where('id',$oldEntry['product_id'])->first();

	$inventoryDetail = Inventory::where('entry_id',$oldEntry['id'])->first();

	$quantity = $inventoryDetail->entries - $inventoryDetail->outputs;

    Log::info('inventory detail a eliminar, entradas: ' . $inventoryDetail->entries . '/ Salidas: ' . $inventoryDetail->outputs );





        $inventoryGeneral = InventoryGeneral::where('product_id',$oldEntry['product_id'])
        ->where('entity_code',$oldEntry['entity_code'])
        ->first();

        Log::info('Este es el entrada general justo antes de eliminar entrada ' . $inventoryGeneral->entries);


        if($oldEntry['condition_id'] == 1)
        {
            $inventoryGeneral->update([
                'stock_good' => $inventoryGeneral->stock_good - $quantity,
                'stock' => $inventoryGeneral->stock - $quantity,
                'entries' => $inventoryGeneral->entries - $quantity,

            ]);
        }

        elseif($oldEntry['condition_id'] == 2)
        {
            $inventoryGeneral->update([
                'stock_bad' => $inventoryGeneral->stock_bad - $quantity,
                'stock' => $inventoryGeneral->stock - $quantity,
                'entries' => $inventoryGeneral->entries - $quantity,

            ]);

        }

        elseif($oldEntry['condition_id'] == 3)
        {
            $inventoryGeneral->update([
                'stock_expired' => $inventoryGeneral->stock_expired - $quantity,
                'stock' => $inventoryGeneral->stock - $quantity,
                'entries' => $inventoryGeneral->entries - $quantity,

            ]);

        }
        elseif($oldEntry['condition_id'] == 4)
        {
            $inventoryGeneral->update([
                'stock_per_expire' => $inventoryGeneral->stock_per_expire - $quantity,
                'stock' => $inventoryGeneral->stock - $quantity,
                'entries' => $inventoryGeneral->entries - $quantity,
            ]);
        }


        if($product->minimum_stock >= $inventoryGeneral->stock_good)
            $inventoryGeneral->update(['minimum_alert' => 1]);


        $inventoryDetail->update(['stock' => 0, 'entries' => $inventoryDetail->outputs]);


            Log::info('Este es el entrada general al eliminar entrada ' . $inventoryGeneral->entries);
            Log::info('El quantity calculado ' . $quantity);
            Log::info('El stock ' . $inventoryGeneral->stock);

    }
}
