<?php

namespace App\Console\Commands;

use App\Models\Entry;
use App\Models\Output;
use App\Models\Product;
use App\Models\Inventory;
use App\Models\HierarchyEntity;
use App\Models\ProductRelation;
use Illuminate\Console\Command;
use App\Models\InventoryGeneral;
use Illuminate\Support\Facades\DB;
use App\Listeners\SubtractInventory;

class ResolveDuplicateProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'resolve:duplicate-products {badCode : Codigo del producto a reemplazar} {goodCode : Codigo del producto a persistir}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Hacer merge de 2 productos repetidos';

    /**
     * Execute the console command.
     */
    public function handle()
    {


        $badCode = $this->argument('badCode');
        $goodCode = $this->argument('goodCode');

        DB::transaction(function () use($badCode, $goodCode) {

        try {

            $entities = HierarchyEntity::get();

            $badProduct = Product::where('code',$badCode)->first();
            $goodProduct = Product::where('code', $goodCode)->first();

            if($badProduct->type_product == 1) //Macro
            {
                ProductRelation::where('product_macro_id', $badProduct)->delete();
            }
            else{
                ProductRelation::where('product_micro_id', $badProduct)->delete();
            }

            foreach ($entities as $entity) {

                $exists = Entry::where('entity_code', $entity->code)
                ->where('product_id', $badProduct->id)
                ->exists();

                if($exists == false)
                    continue;


                Entry::where('entity_code', $entity->code)
                ->where('product_id', $badProduct->id)
                ->update(['product_id' => $goodProduct->id]);

                Output::where('entity_code', $entity->code)
                ->where('product_id', $badProduct->id)
                ->update(['product_id' => $goodProduct->id]);

                Inventory::where('entity_code', $entity->code)
                ->where('product_id', $badProduct->id)
                ->update(['product_id' => $goodProduct->id]);

                $generalBad = InventoryGeneral::where('entity_code', $entity->code)
                ->where('product_id', $badProduct->id)
                ->first();

                $generalGood = InventoryGeneral::where('entity_code', $entity->code)
                ->where('product_id', $goodProduct->id)
                ->first();

                $generalGood->update([
                    'stock_per_expire' => $generalGood->stock_per_expire + $generalBad->stock_per_expire,
                    'stock_bad' => $generalGood->stock_bad + $generalBad->stock_bad,
                    'stock_good' => $generalGood->stock_good + $generalBad->stock_good,
                    'stock' => $generalGood->stock + $generalBad->stock,
                    'entries' => $generalGood->entries + $generalBad->entries,
                    'outputs' => $generalGood->outputs + $generalBad->outputs,
                ]);

                if($generalGood->stock_good <= $goodProduct->minimum_stock){

                    $generalGood->minimum_alert = 1;

                    if($generalGood->stock_good == 0){

                        $subtractInventory = new SubtractInventory();
                        $subtractInventory->sendNotification($goodProduct,$generalGood);
                    }
                }
                else
                    $generalGood->minimum_alert = 0;

                $generalGood->save();
                $generalBad->delete();

                $this->info('Productos resueltos correctamente');

            }
        } catch (\Exception $e) {
                // Lanzar la excepción para que se realice el rollback
                $this->error('UN ERROR INTENTANDO RESOLVER DUPLICACION DE PRODUCTOS');
                $this->error($e->getMessage());
                throw $e;

            }
        });

    }
}
