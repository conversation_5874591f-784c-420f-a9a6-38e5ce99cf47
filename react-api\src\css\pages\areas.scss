.lettersContainer {
    // color: red !;
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    & *:hover {
        cursor: pointer;
    }
    label {
        width: 25px;
        aspect-ratio: 1/1;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
        font-size: 15px;
        color: rgb(83, 83, 83);
        background: rgb(240, 240, 240);
        &:hover {
            background: #d3d3d3;
        }
        span {
            top: 2px;
            position: relative;
        }
        &.active {
            color: rgb(243, 243, 243);
            background-color: rgb(12, 12, 12);
            font-weight: bold;
        }
    }
}
.container_selectAndLetters {
    // width: 100%;
    // display: flex;
    padding-block: 10px;
}
form.areas .MuiSelect-select {
    padding: 8px !important;   
} 
.select_container {  
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}
.number_list {  
    // padding: 5px 20px;
    display: flex;
    padding-block: 3px;
    // height: 20px;
    box-sizing: content-box;
    align-items: center;
    padding: 5px 20px;
    background: rgb(204, 204, 204);
    justify-content: center;
    position: relative;
    left: -16px;
}
.close_schedule {
    background: rgb(204, 204, 204);
    height: 100%;
    // padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

form ul.horarios li {
    display: grid;
    grid-template-columns: 20px auto 30px;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    border: 1px solid rgb(233, 233, 233);

    &.active {
        .number_list {
            font-weight: bold;
            background: #069dbf;
            color: whitesmoke;

        }
        .close_schedule:hover {
            &:hover {
                background: rgb(94, 2, 2);
                color: whitesmoke;
                cursor: pointer;
            }
        }
    }
}
.title {
    text-align: center;
    margin-bottom: 20px;
    text-transform: uppercase;
    opacity: 0.8;
}
