# SISMED 

Web application for Inventory management of Secretaria de Salud (Laravel + React)

## Installation

Use the dependency manager [Composer](https://getcomposer.org/) to install <PERSON><PERSON>.

```bash
git clone https://github.com/JDonquis/SISMED.git
```

```bash
cd sismed-api
cp .env.example .env
composer install
```
Set values of PostgreSQL database on .env file and then:

```bash
php artisan migrate --seed
```

```bash
php artisan serve
```

Create a new terminal window and activate react:

```bash
cd react-api
npm install
npm run dev
```

## **Tools**  
- <PERSON><PERSON> 10  
- React  
- Tailwind CSS 



![Screenshot SISMED](sismed.png)  

## **Get in touch**  
[<EMAIL>](mailto:<EMAIL>)

[<EMAIL>](mailto:<EMAIL>)  

## License

Copyright (c) Secretaria de Salud. All rights reserved.