### Archivos del sistema ###
/.phpunit.cache
.phpunit.result.cache
.DS_Store
.idea
.fleet
.vscode
*.swp
*.swo
Thumbs.db

### Archivos de entorno ###
.env
.env.*.local
.env.backup
.env.production
.env.testing

### Dependencias ###
/node_modules
/vendor
/composer.lock
/auth.json

### Frontend/build ###
/public/build
/public/hot
/public/storage
/public/mix-manifest.json

### Storage ###
/storage/*.key
/storage/debugbar
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/logs/*
!storage/framework/.gitkeep
!storage/logs/.gitkeep

### Archivos de desarrollo local ###
Homestead.json
Homestead.yaml
docker-compose.override.yml
npm-debug.log
yarn-error.log

### Archivos de pruebas ###
/.phpunit.cache
.phpunit.result.cache
coverage/

### Archivos específicos de Laravel Sail ###
docker-compose.override.yml
.env.sail

### Archivos de IDE ###
.idea
.vscode
.fleet
