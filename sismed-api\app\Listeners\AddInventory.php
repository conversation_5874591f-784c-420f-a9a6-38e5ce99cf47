<?php

namespace App\Listeners;

use App\Models\Inventory;
use App\Models\InventoryGeneral;
use App\Models\Product;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class AddInventory
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        $entry = $event->newEntry;
        $outputs = $event->legacyOutputs;


        Inventory::create([
            'entry_id' => $entry->id,
            'entity_code' => $entry->entity_code,
            'product_id' => $entry->product_id,
            'lote_number' => $entry->lote_number,
            'expiration_date' => $entry->expiration_date,
            'stock' => $entry->quantity - $outputs,
            'condition_id' => $entry->condition_id,
            'origin_id' => $entry->organization_id,
            'search' => $entry->search,
            'entries' => $entry->quantity,
            'outputs' => $outputs,
        ]);

        $quantity = $entry->quantity - $outputs;

                    $stockGood = 0;
                    $stockPerExpire = 0;
                    $stockExpired = 0;
                    $stockBad = 0;

                    if($entry->condition_id == 1)
                    {
                        $stockGood += $quantity;
                    }

                    elseif($entry->condition_id == 2)
                    {
                        $stockBad += $quantity;

                    }

                    elseif($entry->condition_id == 3)
                    {
                        $stockExpired += $quantity;

                    }
                    elseif($entry->condition_id == 4)
                    {
                        $stockPerExpire += $quantity;
                    }



        $alert = 0;
        $product = Product::where('id',$entry->product_id)->first();

        $inventoryGeneral = InventoryGeneral::where('product_id',$entry->product_id)
        ->where('entity_code',$entry->entity_code)
        ->first();

        if(!isset($inventoryGeneral->id)){


            if($product->minimum_stock >= $stockGood)
                $alert = 1;


            InventoryGeneral::create([
                'entity_code' => $entry->entity_code,
                'product_id' => $entry->product_id,
                'stock_expired' => $stockExpired,
                'stock_per_expire' => $stockPerExpire,
                'stock_bad' => $stockBad,
                'stock_good' => $stockGood,
                'stock' => $quantity,
                'entries' => $entry->quantity,
                'outputs' => $outputs,
                'search' => $product->search,
                'minimum_alert' => $alert,

            ]);
        }
        else{



            $inventoryGeneral->update([
                'stock_expired' => $inventoryGeneral->stock_expired + $stockExpired,
                'stock_per_expire' => $inventoryGeneral->stock_per_expire + $stockPerExpire,
                'stock_bad' => $inventoryGeneral->stock_bad + $stockBad,
                'stock_good' => $inventoryGeneral->stock_good + $stockGood,
                'stock' => $inventoryGeneral->stock + $quantity,
                'entries' => $inventoryGeneral->entries + $entry->quantity,
                'minimum_alert' => 0,

            ]);

            if($product->minimum_stock >= $inventoryGeneral->stock_good)
                $inventoryGeneral->update(['minimum_alert' => 1]);

            Log::info('Este es el entrada general al agregar entrada ' . $inventoryGeneral->entries);
            Log::info('El quantity calculado ' . $quantity);
            Log::info('El stock ' . $inventoryGeneral->stock);


        }
    }
}
